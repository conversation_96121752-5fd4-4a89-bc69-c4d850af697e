#!/usr/bin/env python3
"""
Flask API服务器 - 股票数据后端
提供RESTful API接口供前端调用，集成东方财富网数据源
"""

# 禁用代理以避免连接问题
import os
os.environ['HTTP_PROXY'] = ''
os.environ['HTTPS_PROXY'] = ''
os.environ['http_proxy'] = ''
os.environ['https_proxy'] = ''

from flask import Flask, request, jsonify
from flask_cors import CORS
import logging
import sys
from datetime import datetime
import traceback

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入股票数据获取模块
from stock_sentiment_api import (
    get_stock_sentiment_data,
    get_dragon_tiger_data, 
    get_limit_up_data,
    fetch_margin_targets,
    AKSHARE_AVAILABLE
)

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局错误处理
@app.errorhandler(500)
def internal_error(error):
    logger.error(f"Internal server error: {error}")
    logger.error(traceback.format_exc())
    return jsonify({
        'success': False,
        'error': 'Internal server error',
        'message': str(error)
    }), 500

@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'error': 'Not found',
        'message': 'The requested resource was not found'
    }), 404

# 健康检查接口
@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        'success': True,
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'akshare_available': AKSHARE_AVAILABLE
    })

# 股票周期数据接口 - 对应前端的 getTSCycle
@app.route('/api/json/stockext/getTSCycle', methods=['POST'])
def get_ts_cycle():
    """
    获取股票周期数据
    对应前端 getStockData 函数的后端实现
    """
    try:
        request_data = request.get_json() or {}
        logger.info(f"收到getTSCycle请求: {request_data}")
        
        # 解析请求参数
        st_req = request_data.get('stReq', {})
        block_id = st_req.get('sBlockId', '**********')
        size = abs(st_req.get('iSize', 30))  # 确保为正数
        
        # 基于block_id获取相应的股票数据
        # 这里可以根据block_id调用不同的数据获取逻辑
        if block_id == '**********':  # 默认板块
            result = get_stock_sentiment_data()
            
            # 转换为前端期望的格式
            response_data = {
                'stRsp': {
                    'iRet': 0,
                    'sMsg': 'success',
                    'data': result.get('data', [])[:size]
                }
            }
        else:
            # 其他板块的处理逻辑
            response_data = {
                'stRsp': {
                    'iRet': 0,
                    'sMsg': 'success',
                    'data': []
                }
            }
        
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"getTSCycle error: {e}")
        logger.error(traceback.format_exc())
        return jsonify({
            'stRsp': {
                'iRet': -1,
                'sMsg': f'Error: {str(e)}',
                'data': []
            }
        }), 500

# 股票详情接口 - 对应前端的 stockExtDetail
@app.route('/api/json/stockextweb/stockExtDetail', methods=['POST'])
def get_stock_ext_detail():
    """
    获取股票板块详情数据
    对应前端 getStockDetail 函数的后端实现
    """
    try:
        request_data = request.get_json() or {}
        logger.info(f"收到stockExtDetail请求: {request_data}")
        
        # 解析请求参数
        st_req = request_data.get('stReq', {})
        block_id = st_req.get('sExt', '')
        size = st_req.get('iSize', 100)
        
        # 获取板块详情数据
        # 这里可以根据block_id获取特定板块的详细信息
        result = get_stock_sentiment_data()
        
        # 转换为前端期望的格式
        response_data = {
            'stRsp': {
                'iRet': 0,
                'sMsg': 'success',
                'data': result.get('data', [])[:size],
                'blockId': block_id
            }
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"stockExtDetail error: {e}")
        logger.error(traceback.format_exc())
        return jsonify({
            'stRsp': {
                'iRet': -1,
                'sMsg': f'Error: {str(e)}',
                'data': []
            }
        }), 500

# 股票行情接口 - 对应前端的 stockHqSimple
@app.route('/api/json/hq_marketdata/stockHqSimple', methods=['POST'])
def get_stock_hq_simple():
    """
    获取股票简单行情数据
    对应前端 getStockHq 函数的后端实现
    """
    try:
        request_data = request.get_json() or {}
        logger.info(f"收到stockHqSimple请求: {request_data}")
        
        # 解析请求参数
        st_req = request_data.get('stReq', {})
        v_stock = st_req.get('vStock', [])
        
        # 提取股票代码列表
        stock_codes = []
        for stock in v_stock:
            if isinstance(stock, dict) and 'sCode' in stock:
                stock_codes.append(stock['sCode'])
            elif isinstance(stock, str):
                stock_codes.append(stock)
        
        logger.info(f"请求股票代码: {stock_codes}")
        
        # 获取股票行情数据
        # 这里可以调用实际的行情数据API
        result = get_stock_sentiment_data()
        stock_data = result.get('data', [])
        
        # 转换为前端期望的格式
        v_stock_hq = []
        for i, stock in enumerate(stock_data[:len(stock_codes)]):
            code = stock_codes[i] if i < len(stock_codes) else stock.get('code', '')
            v_stock_hq.append({
                'code': code,
                'name': stock.get('name', ''),
                'm1': {
                    '6': 10000000,  # 成交额，单位分
                    '8': 2.5        # 涨跌幅
                }
            })
        
        response_data = {
            'stRsp': {
                'iRet': 0,
                'sMsg': 'success',
                'vStockHq': v_stock_hq
            }
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"stockHqSimple error: {e}")
        logger.error(traceback.format_exc())
        return jsonify({
            'stRsp': {
                'iRet': -1,
                'sMsg': f'Error: {str(e)}',
                'vStockHq': []
            }
        }), 500

# 新的RESTful API接口

# 股票情绪监控数据接口
@app.route('/api/stock/sentiment', methods=['GET'])
def get_sentiment():
    """获取股票情绪监控数据"""
    try:
        date = request.args.get('date')
        result = get_stock_sentiment_data(date)
        return jsonify(result)
    except Exception as e:
        logger.error(f"Sentiment API error: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'data': []
        }), 500

# 龙虎榜数据接口
@app.route('/api/stock/dragon-tiger', methods=['GET'])
def get_dragon_tiger():
    """获取龙虎榜数据"""
    try:
        date = request.args.get('date')
        result = get_dragon_tiger_data(date)
        return jsonify(result)
    except Exception as e:
        logger.error(f"Dragon Tiger API error: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'data': []
        }), 500

# 涨停板数据接口
@app.route('/api/stock/limit-up', methods=['GET'])
def get_limit_up():
    """获取涨停板数据"""
    try:
        date = request.args.get('date')
        result = get_limit_up_data(date)
        return jsonify(result)
    except Exception as e:
        logger.error(f"Limit Up API error: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'data': []
        }), 500

# 两融标的数据接口
@app.route('/api/stock/margin-targets', methods=['GET'])
def get_margin_targets():
    """获取两融标的数据"""
    try:
        targets = fetch_margin_targets()
        return jsonify({
            'success': True,
            'data': list(targets),
            'count': len(targets)
        })
    except Exception as e:
        logger.error(f"Margin Targets API error: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'data': []
        }), 500

# 综合数据接口
@app.route('/api/stock/all-data', methods=['GET'])
def get_all_data():
    """获取所有股票数据的综合接口"""
    try:
        date = request.args.get('date')
        
        # 并行获取所有数据
        sentiment_result = get_stock_sentiment_data(date)
        dragon_tiger_result = get_dragon_tiger_data(date)
        limit_up_result = get_limit_up_data(date)
        margin_targets = fetch_margin_targets()
        
        return jsonify({
            'success': True,
            'timestamp': datetime.now().isoformat(),
            'data': {
                'sentiment': sentiment_result,
                'dragon_tiger': dragon_tiger_result,
                'limit_up': limit_up_result,
                'margin_targets': {
                    'success': True,
                    'data': list(margin_targets),
                    'count': len(margin_targets)
                }
            }
        })
    except Exception as e:
        logger.error(f"All Data API error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    logger.info("启动股票数据API服务器...")
    logger.info(f"akshare库状态: {'可用' if AKSHARE_AVAILABLE else '不可用（使用模拟数据）'}")
    
    # 在开发环境中运行
    app.run(host='0.0.0.0', port=5000, debug=True)