#!/usr/bin/env python3
"""
股票情绪监控数据获取脚本
使用akshare库获取东方财富网的股票数据

安装依赖：
pip install akshare pandas requests

使用方法：
python stock_sentiment_api.py [date]
例如：python stock_sentiment_api.py 2025-07-18
"""

import requests
import pandas as pd
import json
from datetime import datetime, timedelta
import sys
import time
import random
from urllib3.util.retry import Retry
from requests.adapters import HTTPAdapter
import warnings
from requests.exceptions import ConnectionError, ProxyError

warnings.filterwarnings('ignore')

PROXY_LIST = []
PROXY_INDEX = 0

def fetch_proxies():
    """
    Fetch a list of proxies from a free proxy provider.
    """
    global PROXY_LIST, PROXY_INDEX
    try:
        print("正在获取代理列表...")
        # Using a free proxy API. This might be unreliable.
        # It's better to use a paid service for production.
        url = "https://proxylist.geonode.com/api/proxy-list?limit=50&page=1&sort_by=lastChecked&sort_type=desc&protocols=http,https"
        session = get_global_session()
        response = session.get(url, timeout=10)
        response.raise_for_status()
        try:
            data = response.json()
        except json.JSONDecodeError:
            print(f"获取代理列表失败: 无法解析JSON，响应内容: {response.text}")
            PROXY_LIST = []
            return False
            
        proxies = []
        for item in data.get('data', []):
            proxies.append(f"http://{item['ip']}:{item['port']}")
        PROXY_LIST = list(set(proxies)) # remove duplicates
        random.shuffle(PROXY_LIST)
        PROXY_INDEX = 0
        if PROXY_LIST:
            print(f"成功获取 {len(PROXY_LIST)} 个代理。")
        else:
            print("未能从API获取到代理。")
        return True
    except Exception as e:
        print(f"获取代理列表失败: {e}")
        PROXY_LIST = []
        return False

def get_next_proxy():
    """
    Get the next proxy from the list in a round-robin fashion.
    """
    global PROXY_INDEX
    if not PROXY_LIST:
        return None
    
    if PROXY_INDEX >= len(PROXY_LIST):
        print("已尝试所有代理，将重新获取代理列表。")
        fetch_proxies()
        if not PROXY_LIST:
            return None
    
    proxy = PROXY_LIST[PROXY_INDEX]
    PROXY_INDEX += 1
    return proxy

# 创建稳定的HTTP会话，解决连接中断问题
def create_robust_session():
    """创建一个健壮的requests会话，用于处理网络连接问题"""
    session = requests.Session()
    
    # 配置重试策略
    retry_strategy = Retry(
        total=3,  # 总共重试3次
        backoff_factor=1,  # 重试间隔基数
        status_forcelist=[429, 500, 502, 503, 504],  # 需要重试的HTTP状态码
        allowed_methods=["HEAD", "GET", "OPTIONS"]  # 允许重试的HTTP方法
    )
    
    # 应用重试策略到HTTP和HTTPS
    adapter = HTTPAdapter(
        max_retries=retry_strategy,
        pool_connections=10,  # 连接池大小
        pool_maxsize=20,  # 最大连接数
        pool_block=False  # 非阻塞模式
    )
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    
    # 设置通用请求头，模拟真实浏览器
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
    })
    
    # 禁用代理，避免代理服务器连接问题
    session.proxies = {'http': None, 'https': None}
    
    return session

# 全局会话实例
_global_session = None

def get_global_session():
    """获取全局会话实例"""
    global _global_session
    if _global_session is None:
        _global_session = create_robust_session()
    return _global_session

# 改进的重试装饰器，支持随机延迟避免服务器限流
def retry_with_backoff(max_retries=3, base_delay=1):
    """带有指数退避和随机抖动的重试装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except (ConnectionError, ProxyError) as e:
                    print(f"第{attempt + 1}次尝试失败，出现连接错误: {e}")
                    
                    if attempt == max_retries - 1:
                        print("已达到最大重试次数，放弃。")
                        raise e

                    if not PROXY_LIST:
                        print("代理列表为空，正在尝试获取...")
                        fetch_proxies()
                        if not PROXY_LIST:
                            print("无法获取代理，将不使用代理重试。")
                            time.sleep(base_delay * (2 ** attempt))
                            continue

                    proxy = get_next_proxy()
                    if proxy:
                        print(f"尝试使用新代理: {proxy}")
                        proxies = {"http": proxy, "https": proxy}
                        if AKSHARE_AVAILABLE:
                            import akshare as ak
                            ak.set_proxies(proxies=proxies)
                    else:
                        print("无法获取新代理，将不使用代理重试。")
                        # 如果没有代理，则不设置代理
                        if AKSHARE_AVAILABLE:
                            import akshare as ak
                            ak.set_proxies(proxies=None)

                    delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
                    print(f"{delay:.2f}秒后重试...")
                    time.sleep(delay)
                except Exception as e:
                    if attempt == max_retries - 1:
                        raise e
                    
                    # 计算延迟时间：指数退避 + 随机抖动
                    delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
                    print(f"第{attempt + 1}次尝试失败: {e}，{delay:.2f}秒后重试...")
                    time.sleep(delay)
            return None
        return wrapper
    return decorator

# 重试装饰器
def retry_on_error(max_retries=3, delay=2):
    def decorator(func):
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries - 1:
                        raise e
                    print(f"第{attempt + 1}次尝试失败: {e}，{delay}秒后重试...")
                    time.sleep(delay)
            return None
        return wrapper
    return decorator

# 保留原有的两融标的获取函数，使用新的会话管理
@retry_with_backoff(max_retries=3, base_delay=1)
def fetch_margin_targets():
    url = "https://datacenter-web.eastmoney.com/api/data/get?type=RPTA_WEB_RZRQ_GGMX&sty=ALL&st=TRADE_DATE&sr=-1&ps=5000&p=1"
    
    try:
        session = get_global_session()
        response = session.get(url, timeout=30)  # 增加超时时间
        response.raise_for_status()
        json_data = response.json()
        
        # 检查数据结构
        if not json_data or "result" not in json_data:
            print("获取两融标的失败: API响应格式错误")
            return set()
        
        result = json_data["result"]
        if not result or "data" not in result:
            print("获取两融标的失败: result数据为空")
            return set()
        
        data = result["data"]
        if not data:
            print("获取两融标的失败: data数据为空")
            return set()
        
        targets = []
        for item in data:
            if item and item.get("IS_MARGIN") == "是" and "SECURITY_CODE" in item:
                targets.append(item['SECURITY_CODE'])
        return set(targets)  # 返回两融标的集合
    except Exception as e:
        print("获取两融标的失败:", e)
        return set()

try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
    print("akshare库可用")
    
    # 配置akshare使用自定义会话，解决连接问题
    def configure_akshare_session():
        """配置akshare使用健壮的会话管理"""
        try:
            # 如果akshare支持会话配置，设置自定义会话
            session = get_global_session()
            
            # 设置akshare的请求参数
            import akshare as ak
            
            # 尝试配置akshare的内部会话（如果支持）
            if hasattr(ak, '_set_session'):
                ak._set_session(session)
            
            # 获取代理列表
            fetch_proxies()
            
            print("akshare会话配置完成")
        except Exception as e:
            print(f"akshare会话配置失败，使用默认配置: {e}")
    
    configure_akshare_session()
    
except ImportError:
    AKSHARE_AVAILABLE = False
    print("警告：akshare库未安装，将使用模拟数据")
    print("安装命令：pip install akshare")

# 健壮的akshare数据获取包装器
class RobustAkshareClient:
    """健壮的akshare客户端，解决连接中断问题"""
    
    def __init__(self):
        self.session = get_global_session()
        self.max_retries = 3
        self.timeout = 10  # 较短的超时时间，快速失败
    
    @retry_with_backoff(max_retries=2, base_delay=0.5)
    def get_stock_spot_data(self):
        """获取A股实时行情数据"""
        if not AKSHARE_AVAILABLE:
            return None
        
        try:
            # 设置较短的超时时间，避免长时间等待
            import socket
            original_timeout = socket.getdefaulttimeout()
            socket.setdefaulttimeout(self.timeout)
            
            try:
                # 尝试获取数据
                data = ak.stock_zh_a_spot_em()
                return data
            finally:
                # 恢复原始超时设置
                socket.setdefaulttimeout(original_timeout)
                
        except Exception as e:
            print(f"akshare获取实时行情失败: {e}")
            raise e
    
    @retry_with_backoff(max_retries=2, base_delay=0.5)
    def get_lhb_detail_data(self, date_str=None):
        """获取龙虎榜详细数据"""
        if not AKSHARE_AVAILABLE:
            return None
        
        try:
            import socket
            original_timeout = socket.getdefaulttimeout()
            socket.setdefaulttimeout(self.timeout)
            
            try:
                # 尝试多种API调用方式
                try:
                    if date_str:
                        return ak.stock_lhb_detail_em(date=date_str)
                    else:
                        return ak.stock_lhb_detail_em()
                except TypeError:
                    # 如果不支持date参数，尝试无参数调用
                    return ak.stock_lhb_detail_em()
            finally:
                socket.setdefaulttimeout(original_timeout)
                
        except Exception as e:
            print(f"akshare获取龙虎榜失败: {e}")
            # 尝试备用API
            try:
                return ak.stock_lhb_jgmx_em()
            except Exception as e2:
                print(f"akshare备用龙虎榜API也失败: {e2}")
                raise e2
    
    @retry_with_backoff(max_retries=2, base_delay=0.5)
    def get_zt_pool_data(self, date_str=None):
        """获取涨停板数据"""
        if not AKSHARE_AVAILABLE:
            return None
        
        try:
            import socket
            original_timeout = socket.getdefaulttimeout()
            socket.setdefaulttimeout(self.timeout)
            
            try:
                # 尝试多种API调用方式
                try:
                    if date_str:
                        return ak.stock_zt_pool_em(date=date_str)
                    else:
                        return ak.stock_zt_pool_em()
                except TypeError:
                    # 如果不支持date参数，尝试无参数调用
                    return ak.stock_zt_pool_em()
            finally:
                socket.setdefaulttimeout(original_timeout)
                
        except Exception as e:
            print(f"akshare获取涨停板失败: {e}")
            # 尝试备用API
            try:
                return ak.stock_zt_pool_dtgc_em()
            except Exception as e2:
                print(f"akshare备用涨停板API也失败: {e2}")
                raise e2

# 创建全局akshare客户端实例
_akshare_client = None

def get_akshare_client():
    """获取akshare客户端实例"""
    global _akshare_client
    if _akshare_client is None:
        _akshare_client = RobustAkshareClient()
    return _akshare_client

def generate_mock_dragon_tiger_data():
    """生成模拟的龙虎榜数据"""
    return [
        {
            "date": "2025-08-02",
            "code": "300999",
            "name": "金龙鱼",
            "firstStopTime": "2025-08-02 09:30:00",
            "reason": "日涨幅偏离值达7%的证券",
            "totalAmount": "1.26亿",
            "netAmount": "8526万",
            "closingPrice": "45.67",
            "change": "8.55%",
            "turnover": "4.2%",
            "industry": "食品饮料"
        },
        {
            "date": "2025-08-02", 
            "code": "002415",
            "name": "海康威视",
            "firstStopTime": "2025-08-02 10:15:00",
            "reason": "日涨幅偏离值达7%的证券",
            "totalAmount": "2.8亿",
            "netAmount": "1.5亿",
            "closingPrice": "32.18",
            "change": "7.89%",
            "turnover": "3.1%",
            "industry": "电子设备"
        }
    ]

def generate_mock_sentiment_data():
    """生成模拟的股票情绪数据"""
    return [
        {
            "code": "603716",
            "name": "塞力医疗",
            "concept": "医疗器械",
            "T_9": 1.88,
            "T_8": -5.95,
            "T_7": "8天跌",
            "T_6": 0.47,
            "T_5": -3.26,
            "T_4": "首板涨停",
            "T_3": -0.59,
            "T_2": "",
            "T_1": "",
            "T": ""
        },
        {
            "code": "001359",
            "name": "平安电工",
            "concept": "电力设备",
            "T_9": 2.19,
            "T_8": "3天跌",
            "T_7": -3.81,
            "T_6": -2.37,
            "T_5": "首板涨停",
            "T_4": 5.11,
            "T_3": "",
            "T_2": "",
            "T_1": "",
            "T": ""
        },
        {
            "code": "300999",
            "name": "金龙鱼",
            "concept": "食品饮料",
            "T_9": 0.56,
            "T_8": 2.34,
            "T_7": -1.23,
            "T_6": "首板涨停",
            "T_5": -2.11,
            "T_4": 3.45,
            "T_3": -0.87,
            "T_2": "",
            "T_1": "",
            "T": ""
        }
    ]

def get_stock_sentiment_data(date_str=None):
    """
    获取股票情绪监控数据
    
    Args:
        date_str: 日期字符串，格式'YYYY-MM-DD'，默认为今天
    
    Returns:
        dict: 包含股票情绪数据的字典
    """
    try:
        if not date_str:
            date_str = datetime.now().strftime('%Y%m%d')
        else:
            date_str = date_str.replace('-', '')
        
        print(f"正在获取 {date_str} 的股票情绪数据...")
        
        if AKSHARE_AVAILABLE:
            # 使用健壮的akshare客户端获取数据
            akshare_client = get_akshare_client()
            stock_zh_a_spot_em_df = akshare_client.get_stock_spot_data()
            
            if stock_zh_a_spot_em_df is None:
                print("未获取到实时股票数据，使用模拟数据")
            
            # 处理数据，模拟T-9到T的情绪值
            sentiment_data = []
            
            if stock_zh_a_spot_em_df is not None and not stock_zh_a_spot_em_df.empty:
                for index, row in stock_zh_a_spot_em_df.head(20).iterrows():  # 取前20只股票作为示例
                    try:
                        # 模拟情绪数据，实际应该根据具体算法计算
                        code = row['代码']
                        name = row['名称']
                        change_pct = row['涨跌幅']
                        
                        # 根据涨跌幅模拟情绪等级
                        def get_sentiment_level(pct):
                            if pct > 8:
                                return "首板涨停"
                            elif pct > 5:
                                return f"{pct:.2f}"
                            elif pct > 2:
                                return f"{pct:.2f}"
                            elif pct > -2:
                                return f"{pct:.2f}"
                            elif pct > -5:
                                return f"{abs(pct):.0f}天跌"
                            else:
                                return f"{abs(pct):.0f}天跌"
                        
                        sentiment_item = {
                            "code": code,
                            "name": name,
                            "concept": row.get('所属行业', '未知'),
                            "T_9": get_sentiment_level(change_pct * 0.9),
                            "T_8": get_sentiment_level(change_pct * 0.8),
                            "T_7": get_sentiment_level(change_pct * 0.7),
                            "T_6": get_sentiment_level(change_pct * 0.6),
                            "T_5": get_sentiment_level(change_pct * 0.5),
                            "T_4": get_sentiment_level(change_pct * 0.4),
                            "T_3": get_sentiment_level(change_pct * 0.3),
                            "T_2": get_sentiment_level(change_pct * 0.2),
                            "T_1": get_sentiment_level(change_pct * 0.1),
                            "T": get_sentiment_level(change_pct)
                        }
                        
                        sentiment_data.append(sentiment_item)
                        
                    except Exception as e:
                        print(f"处理股票 {row.get('代码', 'unknown')} 时出错: {e}")
                        continue
            else:
                print("未获取到实时股票数据，使用模拟数据")
                # 立即使用模拟数据，避免重复逻辑
                sentiment_data = generate_mock_sentiment_data()
        else:
            # akshare不可用时使用模拟数据
            print("akshare不可用，使用模拟数据")
            sentiment_data = generate_mock_sentiment_data()
        
        return {
            "success": True,
            "data": sentiment_data,
            "count": len(sentiment_data),
            "date": date_str
        }
        
    except Exception as e:
        print(f"获取股票情绪数据失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "data": [],
            "count": 0,
            "date": date_str if date_str else datetime.now().strftime('%Y%m%d')
        }

def get_dragon_tiger_data(date_str=None):
    """
    获取龙虎榜数据
    
    Args:
        date_str: 日期字符串，格式'YYYY-MM-DD'
    
    Returns:
        dict: 包含龙虎榜数据的字典
    """
    try:
        if not date_str:
            date_str = datetime.now().strftime('%Y%m%d')
        else:
            date_str = date_str.replace('-', '')
        
        print(f"正在获取 {date_str} 的龙虎榜数据...")
        
        dragon_tiger_data = []  # 初始化变量
        
        if AKSHARE_AVAILABLE:
            # 使用健壮的akshare客户端获取龙虎榜数据
            akshare_client = get_akshare_client()
            stock_lhb_detail_em_df = akshare_client.get_lhb_detail_data(date_str)
            
            if stock_lhb_detail_em_df is not None and not stock_lhb_detail_em_df.empty:
                for index, row in stock_lhb_detail_em_df.head(10).iterrows():
                    try:
                        # 尝试不同的列名映射
                        code = row.get('代码') or row.get('symbol') or row.get('股票代码') or 'unknown'
                        name = row.get('名称') or row.get('name') or row.get('股票名称') or 'unknown'
                        reason = row.get('上榜原因') or row.get('原因') or row.get('reason') or '未知'
                        industry = row.get('所属行业') or row.get('行业') or row.get('industry') or '未知'
                        close_price = row.get('收盘价') or row.get('close') or row.get('价格') or 0
                        
                        dragon_tiger_item = {
                            "date": date_str[:4] + '-' + date_str[4:6] + '-' + date_str[6:8],
                            "code": str(code),
                            "name": str(name),
                            "firstStopTime": f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]} 09:30:00",
                            "lastStopTime": f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]} 15:00:00",
                            "continuousDays": 1,  # 实际需要计算连续涨停天数
                            "type": "1天1板",
                            "reason": str(reason),
                            "category": str(industry),
                            "openPrice": float(close_price) if close_price else 0.0,
                            "actions": "修改 删除"
                        }
                        
                        dragon_tiger_data.append(dragon_tiger_item)
                        
                    except Exception as e:
                        print(f"处理龙虎榜记录时出错: {e}")
                        continue
            else:
                print("龙虎榜API返回空数据，使用模拟数据")
                
        else:
            dragon_tiger_data = generate_mock_dragon_tiger_data()
        
        # 如果没有获取到数据，使用模拟数据
        if not dragon_tiger_data:
            dragon_tiger_data = generate_mock_dragon_tiger_data()
        
        return {
            "success": True,
            "data": dragon_tiger_data,
            "count": len(dragon_tiger_data),
            "date": date_str
        }
        
    except Exception as e:
        print(f"获取龙虎榜数据失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "data": [],
            "count": 0,
            "date": date_str if date_str else datetime.now().strftime('%Y%m%d')
        }

def get_limit_up_data(date_str=None):
    """
    获取涨停板数据
    
    Args:
        date_str: 日期字符串，格式'YYYY-MM-DD'
    
    Returns:
        dict: 包含涨停板数据的字典
    """
    try:
        if not date_str:
            date_str = datetime.now().strftime('%Y%m%d')
        else:
            date_str = date_str.replace('-', '')
        
        print(f"正在获取 {date_str} 的涨停板数据...")
        
        if AKSHARE_AVAILABLE:
            # 使用健壮的akshare客户端获取涨停板数据
            akshare_client = get_akshare_client()
            stock_zt_pool_em_df = akshare_client.get_zt_pool_data(date_str)
            
            limit_up_data = []
            
            if stock_zt_pool_em_df is not None and not stock_zt_pool_em_df.empty:
                for index, row in stock_zt_pool_em_df.head(10).iterrows():
                    try:
                        # 兼容不同的列名
                        code = row.get('代码') or row.get('symbol') or 'unknown'
                        name = row.get('名称') or row.get('name') or 'unknown'
                        first_stop = row.get('首次封板时间') or row.get('首板时间') or '09:30:00'
                        last_stop = row.get('最后封板时间') or row.get('末板时间') or '15:00:00'
                        continuous_days = int(row.get('连板数') or row.get('连续涨停') or 1)
                        reason = row.get('涨停原因') or row.get('原因') or '未知'
                        category = row.get('涨停统计') or row.get('分类') or '普通涨停'
                        close_price = row.get('收盘价') or row.get('现价') or row.get('价格') or 0
                        volume = row.get('成交量') or 0
                        turnover = row.get('成交额') or 0
                        turnover_rate = row.get('换手率') or 0
                        amplitude = row.get('振幅') or 0
                        pe_ratio = row.get('市盈率') or 0
                        
                        limit_up_item = {
                            "code": str(code),
                            "name": str(name),
                            "stopDate": date_str[:4] + '-' + date_str[4:6] + '-' + date_str[6:8],
                            "firstStopTime": f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]} {first_stop}",
                            "lastStopTime": f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]} {last_stop}",
                            "continuousDays": continuous_days,
                            "type": f"{continuous_days}天{continuous_days}板",
                            "reason": str(reason),
                            "category": str(category),
                            "openPrice": float(close_price) if close_price else 0.0,
                            "volume": int(volume) if volume else 0,
                            "turnover": float(turnover) if turnover else 0.0,
                            "turnoverRate": float(turnover_rate) if turnover_rate else 0.0,
                            "amplitude": float(amplitude) if amplitude else 0.0,
                            "peRatio": float(pe_ratio) if pe_ratio else 0.0,
                            "dragonTigerCount": 0  # 需要额外查询
                        }
                        
                        limit_up_data.append(limit_up_item)
                        
                    except Exception as e:
                        print(f"处理涨停板记录时出错: {e}")
                        continue
            else:
                print("涨停板API返回空数据，使用模拟数据")
                
        else:
            limit_up_data = []
        
        # 如果没有获取到数据，使用模拟数据
        if not limit_up_data:
            limit_up_data = [
                {
                    "code": "002265",
                    "name": "西仪股份",
                    "stopDate": date_str[:4] + '-' + date_str[4:6] + '-' + date_str[6:8],
                    "firstStopTime": f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]} 09:30:15",
                    "lastStopTime": f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]} 09:46:30",
                    "continuousDays": 2,
                    "type": "2天2板",
                    "reason": "一字涨停，龙头涨停",
                    "category": "一字涨停",
                    "openPrice": 33.53,
                    "volume": 123456,
                    "turnover": 87654321,
                    "turnoverRate": 5.67,
                    "amplitude": 12.34,
                    "peRatio": 25.8,
                    "dragonTigerCount": 5
                }
            ]
        
        return {
            "success": True,
            "data": limit_up_data,
            "count": len(limit_up_data),
            "date": date_str
        }
        
    except Exception as e:
        print(f"获取涨停板数据失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "data": [],
            "count": 0,
            "date": date_str if date_str else datetime.now().strftime('%Y%m%d')
        }

def main():
    """主函数"""
    try:
        # 获取命令行参数
        date_str = sys.argv[1] if len(sys.argv) > 1 else None
        
        print("开始获取股票数据...")
        print(f"akshare库状态: {'可用' if AKSHARE_AVAILABLE else '不可用（使用模拟数据）'}")
        
        # 获取两融标的
        print("\n获取两融标的数据...")
        margin_targets = fetch_margin_targets()
        print(f"共获取 {len(margin_targets)} 只两融标的")
        
        # 获取各种数据
        sentiment_result = get_stock_sentiment_data(date_str)
        dragon_tiger_result = get_dragon_tiger_data(date_str)
        limit_up_result = get_limit_up_data(date_str)
        
        # 输出结果
        result = {
            "timestamp": datetime.now().isoformat(),
            "margin_targets": {
                "success": True,
                "data": list(margin_targets),
                "count": len(margin_targets)
            },
            "sentiment": sentiment_result,
            "dragon_tiger": dragon_tiger_result,
            "limit_up": limit_up_result
        }
        
        # 保存到文件
        output_file = f"stock_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"\n数据已保存到: {output_file}")
        print(f"两融标的: {len(margin_targets)} 只")
        print(f"情绪监控数据: {sentiment_result['count']} 条")
        print(f"龙虎榜数据: {dragon_tiger_result['count']} 条")
        print(f"涨停板数据: {limit_up_result['count']} 条")
        
        # 输出简化的JSON供前端使用
        print("\n=== 前端API格式数据 ===")
        api_result = {
            "sentiment_data": sentiment_result['data'],
            "dragon_tiger_data": dragon_tiger_result['data'],
            "limit_up_data": limit_up_result['data']
        }
        print(json.dumps(api_result, ensure_ascii=False, indent=2))
        
    except Exception as e:
        print(f"执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()