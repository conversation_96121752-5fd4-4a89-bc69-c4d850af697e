INFO:__main__:启动股票数据API服务器...
INFO:__main__:akshare库状态: 可用
akshare库可用
正在获取代理列表...
获取代理列表失败: HTTPSConnectionPool(host='api.proxyscrape.com', port=443): Max retries exceeded with url: /v2/?request=getproxies&protocol=http&timeout=10000&country=all&ssl=all&anonymity=all (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x71d6e11c0380>: Failed to establish a new connection: [Errno 101] Network is unreachable'))
akshare会话配置完成
 * Serving Flask app 'app'
 * Debug mode: on
INFO:werkzeug:[31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
INFO:werkzeug:[33mPress CTRL+C to quit[0m
INFO:werkzeug: * Restarting with stat
INFO:__main__:启动股票数据API服务器...
INFO:__main__:akshare库状态: 可用
WARNING:werkzeug: * Debugger is active!
INFO:werkzeug: * Debugger PIN: 849-779-734
INFO:werkzeug: * Detected change in '/mnt/d/proj/wsl/pixiu-web/be/stock_sentiment_api.py', reloading
akshare库可用
正在获取代理列表...
获取代理列表失败: HTTPSConnectionPool(host='api.proxyscrape.com', port=443): Max retries exceeded with url: /v2/?request=getproxies&protocol=http&timeout=10000&country=all&ssl=all&anonymity=all (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x719179eb80e0>: Failed to establish a new connection: [Errno 101] Network is unreachable'))
akshare会话配置完成
INFO:werkzeug: * Restarting with stat
INFO:__main__:启动股票数据API服务器...
INFO:__main__:akshare库状态: 可用
WARNING:werkzeug: * Debugger is active!
INFO:werkzeug: * Debugger PIN: 849-779-734
INFO:werkzeug:127.0.0.1 - - [03/Aug/2025 00:53:04] "GET /api/stock/margin-targets?date=2025-08-03 HTTP/1.1" 200 -
akshare库可用
正在获取代理列表...
获取代理列表失败: HTTPSConnectionPool(host='api.proxyscrape.com', port=443): Max retries exceeded with url: /v2/?request=getproxies&protocol=http&timeout=10000&country=all&ssl=all&anonymity=all (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7d020a5f43e0>: Failed to establish a new connection: [Errno 101] Network is unreachable'))
akshare会话配置完成
正在获取 20250803 的股票情绪数据...
正在获取 20250803 的龙虎榜数据...
正在获取 20250803 的涨停板数据...
正在获取 20250803 的股票情绪数据...
获取两融标的失败: result数据为空
正在获取 20250803 的龙虎榜数据...
正在获取 20250803 的涨停板数据...

  0%|          | 0/57 [00:00<?, ?it/s]

  0%|          | 0/57 [00:00<?, ?it/s][AINFO:werkzeug:127.0.0.1 - - [03/Aug/2025 00:53:04] "GET /api/stock/limit-up?date=2025-08-03 HTTP/1.1" 200 -
INFO:__main__:收到getTSCycle请求: {'stReq': {'stHeader': {'sXua': 'Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) /Chrome/91.0.4472(Windows) guniuniu_hummer_pc/1.4.8.2735 Safari/537.36'}, 'sBlockId': '9999900003', 'eColumn': 4, 'eSort': 2, 'lDate': 0, 'iSize': -30, 'iCount': 30, 'bRemoveTS': True}}
正在获取 20250803 的股票情绪数据...



  0%|          | 0/2 [00:00<?, ?it/s][A[A
  2%|▏         | 1/57 [00:00<00:12,  4.55it/s]

  2%|▏         | 1/57 [00:00<00:12,  4.59it/s][AINFO:werkzeug:127.0.0.1 - - [03/Aug/2025 00:53:05] "GET /api/stock/limit-up?date=2025-08-03 HTTP/1.1" 200 -




  0%|          | 0/57 [00:00<?, ?it/s][A[A[A




  0%|          | 0/2 [00:00<?, ?it/s][A[A[A[A
  4%|▎         | 2/57 [00:00<00:10,  5.04it/s]

  4%|▎         | 2/57 [00:00<00:10,  5.11it/s][A


 50%|█████     | 1/2 [00:00<00:00,  3.05it/s][A[A



  2%|▏         | 1/57 [00:00<00:14,  3.82it/s][A[A[A
  5%|▌         | 3/57 [00:00<00:09,  5.55it/s]

  5%|▌         | 3/57 [00:00<00:11,  4.81it/s][A



  4%|▎         | 2/57 [00:00<00:10,  5.19it/s][A[A[A


100%|██████████| 2/2 [00:00<00:00,  3.66it/s][A[A


                                             [A[AINFO:werkzeug:127.0.0.1 - - [03/Aug/2025 00:53:05] "GET /api/stock/dragon-tiger?date=2025-08-03 HTTP/1.1" 200 -

  7%|▋         | 4/57 [00:00<00:09,  5.60it/s]




 50%|█████     | 1/2 [00:00<00:00,  2.32it/s][A[A[A[A

  7%|▋         | 4/57 [00:00<00:09,  5.54it/s][A
  9%|▉         | 5/57 [00:00<00:08,  6.02it/s]



  5%|▌         | 3/57 [00:00<00:10,  5.03it/s][A[A[A

  9%|▉         | 5/57 [00:00<00:08,  6.18it/s][A
 11%|█         | 6/57 [00:01<00:08,  6.07it/s]



  7%|▋         | 4/57 [00:00<00:09,  5.35it/s][A[A[A

 11%|█         | 6/57 [00:01<00:08,  5.97it/s][A
 12%|█▏        | 7/57 [00:01<00:08,  6.25it/s]




100%|██████████| 2/2 [00:00<00:00,  2.26it/s][A[A[A[A




                                             [A[A[A[AINFO:werkzeug:127.0.0.1 - - [03/Aug/2025 00:53:06] "GET /api/stock/dragon-tiger?date=2025-08-03 HTTP/1.1" 200 -




  9%|▉         | 5/57 [00:00<00:09,  5.63it/s][A[A[A

 12%|█▏        | 7/57 [00:01<00:08,  6.16it/s][A

 14%|█▍        | 8/57 [00:01<00:07,  6.16it/s][A



 11%|█         | 6/57 [00:01<00:09,  5.62it/s][A[A[A
 14%|█▍        | 8/57 [00:01<00:09,  5.06it/s]



 12%|█▏        | 7/57 [00:01<00:08,  5.72it/s][A[A[A
 16%|█▌        | 9/57 [00:01<00:08,  5.34it/s]

 16%|█▌        | 9/57 [00:01<00:09,  5.27it/s][A



 14%|█▍        | 8/57 [00:01<00:08,  5.79it/s][A[A[A
 18%|█▊        | 10/57 [00:01<00:08,  5.86it/s]

 18%|█▊        | 10/57 [00:01<00:08,  5.67it/s][A



 16%|█▌        | 9/57 [00:01<00:07,  6.13it/s][A[A[A
 19%|█▉        | 11/57 [00:01<00:07,  5.92it/s]

 19%|█▉        | 11/57 [00:02<00:09,  5.09it/s][A



 18%|█▊        | 10/57 [00:01<00:07,  6.26it/s][A[A[A
 21%|██        | 12/57 [00:02<00:07,  6.33it/s]



 19%|█▉        | 11/57 [00:01<00:07,  6.34it/s][A[A[A

 21%|██        | 12/57 [00:02<00:08,  5.30it/s][A
 23%|██▎       | 13/57 [00:02<00:06,  6.46it/s]

 23%|██▎       | 13/57 [00:02<00:07,  5.68it/s][A
 25%|██▍       | 14/57 [00:02<00:06,  6.53it/s]



 21%|██        | 12/57 [00:02<00:08,  5.52it/s][A[A[A

 25%|██▍       | 14/57 [00:02<00:07,  6.04it/s][A
 26%|██▋       | 15/57 [00:02<00:06,  6.66it/s]



 23%|██▎       | 13/57 [00:02<00:07,  5.60it/s][A[A[A

 26%|██▋       | 15/57 [00:02<00:06,  6.29it/s][A
 28%|██▊       | 16/57 [00:02<00:06,  6.47it/s]



 25%|██▍       | 14/57 [00:02<00:07,  5.79it/s][A[A[A
 30%|██▉       | 17/57 [00:02<00:05,  6.73it/s]

 28%|██▊       | 16/57 [00:02<00:07,  5.67it/s][A



 26%|██▋       | 15/57 [00:02<00:06,  6.31it/s][A[A[A
 32%|███▏      | 18/57 [00:02<00:05,  6.88it/s]

 30%|██▉       | 17/57 [00:02<00:06,  6.06it/s][A



 28%|██▊       | 16/57 [00:02<00:06,  5.95it/s][A[A[A
 33%|███▎      | 19/57 [00:03<00:05,  6.87it/s]

 32%|███▏      | 18/57 [00:03<00:06,  6.09it/s][A



 30%|██▉       | 17/57 [00:02<00:06,  6.18it/s][A[A[A
 35%|███▌      | 20/57 [00:03<00:05,  6.72it/s]

 33%|███▎      | 19/57 [00:03<00:06,  6.15it/s][A



 32%|███▏      | 18/57 [00:03<00:06,  6.31it/s][A[A[A
 37%|███▋      | 21/57 [00:03<00:05,  6.61it/s]

 35%|███▌      | 20/57 [00:03<00:06,  6.15it/s][A
 39%|███▊      | 22/57 [00:03<00:05,  6.75it/s]



 33%|███▎      | 19/57 [00:03<00:06,  5.92it/s][A[A[A

 37%|███▋      | 21/57 [00:03<00:05,  6.20it/s][A
 40%|████      | 23/57 [00:03<00:05,  6.57it/s]



 35%|███▌      | 20/57 [00:03<00:06,  6.00it/s][A[A[A

 39%|███▊      | 22/57 [00:03<00:05,  6.10it/s][A
 42%|████▏     | 24/57 [00:03<00:05,  6.47it/s]



 37%|███▋      | 21/57 [00:03<00:06,  5.70it/s][A[A[A

 40%|████      | 23/57 [00:04<00:06,  5.61it/s][A
 44%|████▍     | 25/57 [00:04<00:04,  6.54it/s]



 39%|███▊      | 22/57 [00:03<00:05,  5.90it/s][A[A[A

 42%|████▏     | 24/57 [00:04<00:05,  5.85it/s][A
 46%|████▌     | 26/57 [00:04<00:04,  6.39it/s]



 40%|████      | 23/57 [00:03<00:05,  5.93it/s][A[A[A

 44%|████▍     | 25/57 [00:04<00:05,  5.98it/s][A
 47%|████▋     | 27/57 [00:04<00:04,  6.15it/s]



 42%|████▏     | 24/57 [00:04<00:05,  6.25it/s][A[A[A

 46%|████▌     | 26/57 [00:04<00:05,  6.18it/s][A



 44%|████▍     | 25/57 [00:04<00:05,  6.40it/s][A[A[A
 49%|████▉     | 28/57 [00:04<00:04,  6.01it/s]

 47%|████▋     | 27/57 [00:04<00:04,  6.11it/s][A



 46%|████▌     | 26/57 [00:04<00:04,  6.42it/s][A[A[A
 51%|█████     | 29/57 [00:04<00:05,  5.16it/s]

 49%|████▉     | 28/57 [00:04<00:04,  5.97it/s][A



 47%|████▋     | 27/57 [00:04<00:04,  6.15it/s][A[A[A
 53%|█████▎    | 30/57 [00:04<00:05,  5.39it/s]

 51%|█████     | 29/57 [00:05<00:04,  5.68it/s][A



 49%|████▉     | 28/57 [00:04<00:04,  5.92it/s][A[A[A
 54%|█████▍    | 31/57 [00:05<00:04,  5.41it/s]

 53%|█████▎    | 30/57 [00:05<00:04,  5.66it/s][A



 51%|█████     | 29/57 [00:05<00:05,  5.20it/s][A[A[A
 56%|█████▌    | 32/57 [00:05<00:04,  5.43it/s]

 54%|█████▍    | 31/57 [00:05<00:04,  5.84it/s][A



 53%|█████▎    | 30/57 [00:05<00:05,  5.38it/s][A[A[A
 58%|█████▊    | 33/57 [00:05<00:04,  5.51it/s]

 56%|█████▌    | 32/57 [00:05<00:04,  5.88it/s][A



 54%|█████▍    | 31/57 [00:05<00:04,  5.39it/s][A[A[A
 60%|█████▉    | 34/57 [00:05<00:04,  5.52it/s]

 58%|█████▊    | 33/57 [00:05<00:04,  5.79it/s][A
 61%|██████▏   | 35/57 [00:05<00:04,  5.47it/s]

 60%|█████▉    | 34/57 [00:05<00:04,  5.74it/s][A



 56%|█████▌    | 32/57 [00:05<00:05,  4.64it/s][A[A[A
 63%|██████▎   | 36/57 [00:06<00:03,  5.44it/s]

 61%|██████▏   | 35/57 [00:06<00:03,  5.52it/s][A



 58%|█████▊    | 33/57 [00:05<00:04,  4.96it/s][A[A[A
 65%|██████▍   | 37/57 [00:06<00:03,  5.45it/s]

 63%|██████▎   | 36/57 [00:06<00:03,  5.46it/s][A



 60%|█████▉    | 34/57 [00:05<00:04,  5.15it/s][A[A[A
 67%|██████▋   | 38/57 [00:06<00:03,  5.55it/s]

 65%|██████▍   | 37/57 [00:06<00:03,  5.48it/s][A



 61%|██████▏   | 35/57 [00:06<00:04,  5.08it/s][A[A[A
 68%|██████▊   | 39/57 [00:06<00:03,  5.59it/s]

 67%|██████▋   | 38/57 [00:06<00:03,  5.62it/s][A



 63%|██████▎   | 36/57 [00:06<00:04,  5.18it/s][A[A[A
 70%|███████   | 40/57 [00:06<00:02,  5.75it/s]

 68%|██████▊   | 39/57 [00:06<00:03,  5.82it/s][A



 65%|██████▍   | 37/57 [00:06<00:03,  5.60it/s][A[A[A
 72%|███████▏  | 41/57 [00:06<00:02,  5.67it/s]

 70%|███████   | 40/57 [00:06<00:02,  5.90it/s][A



 67%|██████▋   | 38/57 [00:06<00:03,  5.70it/s][A[A[A
 74%|███████▎  | 42/57 [00:07<00:02,  5.72it/s]

 72%|███████▏  | 41/57 [00:07<00:02,  5.73it/s][A



 68%|██████▊   | 39/57 [00:06<00:02,  6.04it/s][A[A[A
 75%|███████▌  | 43/57 [00:07<00:02,  5.94it/s]

 74%|███████▎  | 42/57 [00:07<00:02,  5.71it/s][A



 70%|███████   | 40/57 [00:07<00:03,  5.35it/s][A[A[A

 75%|███████▌  | 43/57 [00:07<00:02,  5.97it/s][A



 72%|███████▏  | 41/57 [00:07<00:02,  5.46it/s][A[A[A
 77%|███████▋  | 44/57 [00:07<00:02,  4.77it/s]

 77%|███████▋  | 44/57 [00:07<00:02,  6.18it/s][A



 74%|███████▎  | 42/57 [00:07<00:02,  5.60it/s][A[A[A
 79%|███████▉  | 45/57 [00:07<00:02,  5.22it/s]

 79%|███████▉  | 45/57 [00:07<00:01,  6.22it/s][A
 81%|████████  | 46/57 [00:07<00:01,  5.52it/s]



 75%|███████▌  | 43/57 [00:07<00:02,  5.71it/s][A[A[A

 81%|████████  | 46/57 [00:07<00:01,  6.37it/s][A
 82%|████████▏ | 47/57 [00:08<00:01,  5.73it/s]



 77%|███████▋  | 44/57 [00:07<00:02,  5.86it/s][A[A[A

 82%|████████▏ | 47/57 [00:08<00:01,  6.27it/s][A

 84%|████████▍ | 48/57 [00:08<00:01,  5.97it/s][A
 84%|████████▍ | 48/57 [00:08<00:01,  5.22it/s]



 79%|███████▉  | 45/57 [00:08<00:02,  4.91it/s][A[A[A
 86%|████████▌ | 49/57 [00:08<00:01,  5.54it/s]

 86%|████████▌ | 49/57 [00:08<00:01,  5.80it/s][A



 81%|████████  | 46/57 [00:08<00:02,  5.33it/s][A[A[A
 88%|████████▊ | 50/57 [00:08<00:01,  5.87it/s]



 82%|████████▏ | 47/57 [00:08<00:01,  5.69it/s][A[A[A

 88%|████████▊ | 50/57 [00:08<00:01,  5.71it/s][A
 89%|████████▉ | 51/57 [00:08<00:00,  6.11it/s]

 89%|████████▉ | 51/57 [00:08<00:00,  6.02it/s][A



 84%|████████▍ | 48/57 [00:08<00:01,  5.76it/s][A[A[A
 91%|█████████ | 52/57 [00:08<00:00,  6.41it/s]

 91%|█████████ | 52/57 [00:08<00:00,  6.23it/s][A



 86%|████████▌ | 49/57 [00:08<00:01,  6.05it/s][A[A[A
 93%|█████████▎| 53/57 [00:09<00:00,  6.17it/s]

 93%|█████████▎| 53/57 [00:09<00:00,  6.18it/s][A



 88%|████████▊ | 50/57 [00:08<00:01,  5.82it/s][A[A[A
 95%|█████████▍| 54/57 [00:09<00:00,  5.52it/s]

 95%|█████████▍| 54/57 [00:09<00:00,  6.02it/s][A



 89%|████████▉ | 51/57 [00:09<00:01,  5.74it/s][A[A[A
 96%|█████████▋| 55/57 [00:09<00:00,  5.73it/s]

 96%|█████████▋| 55/57 [00:09<00:00,  6.03it/s][A



 91%|█████████ | 52/57 [00:09<00:00,  6.05it/s][A[A[A

 98%|█████████▊| 56/57 [00:09<00:00,  6.15it/s][A
 98%|█████████▊| 56/57 [00:09<00:00,  5.79it/s]



 93%|█████████▎| 53/57 [00:09<00:00,  5.99it/s][A[A[A

100%|██████████| 57/57 [00:09<00:00,  6.42it/s][A

                                               [A
100%|██████████| 57/57 [00:09<00:00,  5.61it/s]
                                               
INFO:werkzeug:127.0.0.1 - - [03/Aug/2025 00:53:14] "GET /api/stock/sentiment?date=2025-08-03 HTTP/1.1" 200 -
INFO:werkzeug:127.0.0.1 - - [03/Aug/2025 00:53:14] "GET /api/stock/sentiment?date=2025-08-03 HTTP/1.1" 200 -




 95%|█████████▍| 54/57 [00:09<00:00,  5.31it/s][A[A[A



 96%|█████████▋| 55/57 [00:09<00:00,  4.30it/s][A[A[A



 98%|█████████▊| 56/57 [00:10<00:00,  4.65it/s][A[A[A



100%|██████████| 57/57 [00:10<00:00,  5.09it/s][A[A[A



                                               [A[A[AINFO:werkzeug:127.0.0.1 - - [03/Aug/2025 00:53:15] "POST /api/json/stockext/getTSCycle HTTP/1.1" 200 -
INFO:__main__:收到stockExtDetail请求: {'stReq': {'stHeader': {}, 'lDate': 0, 'iType': 6, 'iId': 6, 'iExt': 0, 'sExt': '未知', 'vFilterType': [], 'vStock': [], 'eColumn': 4, 'eSort': 2, 'iStart': 0, 'iSize': 100, 'bFromCache': True, 'stFrom': {}, 'vBitmap': [0, 0, 0, 0, 0, 0, 64], 'mapIncFlag': {}, 'mapReq': {}, 'bDetail': False, 'iReqType': 1, 'iSaveFlag': 0}}
正在获取 20250803 的股票情绪数据...

  0%|          | 0/57 [00:00<?, ?it/s]
  2%|▏         | 1/57 [00:00<00:09,  5.67it/s]
  4%|▎         | 2/57 [00:03<01:50,  2.01s/it]
  5%|▌         | 3/57 [00:03<01:03,  1.17s/it]
  7%|▋         | 4/57 [00:03<00:40,  1.30it/s]
  9%|▉         | 5/57 [00:03<00:28,  1.80it/s]
 11%|█         | 6/57 [00:04<00:22,  2.27it/s]
 12%|█▏        | 7/57 [00:04<00:17,  2.80it/s]
 14%|█▍        | 8/57 [00:04<00:14,  3.41it/s]
 16%|█▌        | 9/57 [00:04<00:12,  3.95it/s]
 18%|█▊        | 10/57 [00:04<00:10,  4.43it/s]
 19%|█▉        | 11/57 [00:05<00:09,  4.83it/s]
 21%|██        | 12/57 [00:05<00:08,  5.14it/s]
 23%|██▎       | 13/57 [00:05<00:08,  5.34it/s]
 25%|██▍       | 14/57 [00:05<00:07,  5.51it/s]
 26%|██▋       | 15/57 [00:05<00:07,  5.59it/s]
 28%|██▊       | 16/57 [00:05<00:07,  5.60it/s]
 30%|██▉       | 17/57 [00:06<00:06,  5.81it/s]
 32%|███▏      | 18/57 [00:06<00:06,  6.01it/s]
 33%|███▎      | 19/57 [00:06<00:06,  6.06it/s]
 35%|███▌      | 20/57 [00:06<00:06,  6.03it/s]
 37%|███▋      | 21/57 [00:06<00:05,  6.01it/s]
 39%|███▊      | 22/57 [00:06<00:06,  5.71it/s]
 40%|████      | 23/57 [00:07<00:05,  5.81it/s]
 42%|████▏     | 24/57 [00:07<00:05,  5.91it/s]
 44%|████▍     | 25/57 [00:07<00:05,  6.14it/s]
 46%|████▌     | 26/57 [00:07<00:04,  6.26it/s]
 47%|████▋     | 27/57 [00:07<00:04,  6.15it/s]
 49%|████▉     | 28/57 [00:07<00:04,  5.94it/s]
 51%|█████     | 29/57 [00:08<00:04,  5.86it/s]
 53%|█████▎    | 30/57 [00:08<00:04,  5.86it/s]
 54%|█████▍    | 31/57 [00:08<00:04,  5.69it/s]
 56%|█████▌    | 32/57 [00:08<00:04,  5.69it/s]
 58%|█████▊    | 33/57 [00:08<00:04,  5.53it/s]
 60%|█████▉    | 34/57 [00:08<00:04,  5.51it/s]
 61%|██████▏   | 35/57 [00:09<00:04,  5.47it/s]
 63%|██████▎   | 36/57 [00:09<00:03,  5.54it/s]
 65%|██████▍   | 37/57 [00:09<00:03,  5.80it/s]
 67%|██████▋   | 38/57 [00:09<00:03,  6.13it/s]
 68%|██████▊   | 39/57 [00:09<00:02,  6.29it/s]
 70%|███████   | 40/57 [00:09<00:02,  6.38it/s]
 72%|███████▏  | 41/57 [00:10<00:02,  6.10it/s]
 74%|███████▎  | 42/57 [00:10<00:02,  5.96it/s]
 75%|███████▌  | 43/57 [00:10<00:02,  6.09it/s]
 77%|███████▋  | 44/57 [00:10<00:02,  6.34it/s]
 79%|███████▉  | 45/57 [00:10<00:01,  6.52it/s]
 81%|████████  | 46/57 [00:10<00:01,  6.47it/s]
 82%|████████▏ | 47/57 [00:11<00:01,  6.49it/s]
 84%|████████▍ | 48/57 [00:11<00:01,  6.52it/s]
 86%|████████▌ | 49/57 [00:11<00:01,  6.61it/s]
 88%|████████▊ | 50/57 [00:11<00:01,  5.63it/s]
 89%|████████▉ | 51/57 [00:11<00:01,  5.82it/s]
 91%|█████████ | 52/57 [00:11<00:00,  6.00it/s]
 93%|█████████▎| 53/57 [00:12<00:00,  6.22it/s]
 95%|█████████▍| 54/57 [00:12<00:00,  6.52it/s]
 96%|█████████▋| 55/57 [00:12<00:00,  6.81it/s]
 98%|█████████▊| 56/57 [00:12<00:00,  6.94it/s]
100%|██████████| 57/57 [00:12<00:00,  7.02it/s]
                                               
INFO:werkzeug:127.0.0.1 - - [03/Aug/2025 00:53:28] "POST /api/json/stockextweb/stockExtDetail HTTP/1.1" 200 -
正在获取 20250801 的股票情绪数据...
正在获取 20250801 的龙虎榜数据...
正在获取 20250801 的涨停板数据...

  0%|          | 0/57 [00:00<?, ?it/s]INFO:werkzeug:127.0.0.1 - - [03/Aug/2025 00:54:02] "GET /api/stock/limit-up?date=2025-08-01 HTTP/1.1" 200 -

  2%|▏         | 1/57 [00:00<00:09,  5.72it/s]

  0%|          | 0/2 [00:00<?, ?it/s][A
  4%|▎         | 2/57 [00:00<00:08,  6.34it/s]
  5%|▌         | 3/57 [00:00<00:08,  6.55it/s]

 50%|█████     | 1/2 [00:00<00:00,  3.45it/s][A
  7%|▋         | 4/57 [00:00<00:08,  6.41it/s]

100%|██████████| 2/2 [00:00<00:00,  4.09it/s][A

                                             [AINFO:werkzeug:127.0.0.1 - - [03/Aug/2025 00:54:03] "GET /api/stock/dragon-tiger?date=2025-08-01 HTTP/1.1" 200 -

  9%|▉         | 5/57 [00:00<00:08,  6.22it/s]
 11%|█         | 6/57 [00:00<00:08,  6.13it/s]
 12%|█▏        | 7/57 [00:01<00:08,  6.06it/s]
 14%|█▍        | 8/57 [00:01<00:07,  6.36it/s]
 16%|█▌        | 9/57 [00:01<00:07,  6.55it/s]
 18%|█▊        | 10/57 [00:01<00:06,  6.80it/s]
 19%|█▉        | 11/57 [00:01<00:06,  6.91it/s]
 21%|██        | 12/57 [00:01<00:06,  7.04it/s]
 23%|██▎       | 13/57 [00:01<00:06,  7.26it/s]
 25%|██▍       | 14/57 [00:02<00:05,  7.29it/s]
 26%|██▋       | 15/57 [00:02<00:05,  7.30it/s]
 28%|██▊       | 16/57 [00:02<00:05,  7.29it/s]
 30%|██▉       | 17/57 [00:02<00:05,  7.46it/s]
 32%|███▏      | 18/57 [00:02<00:05,  7.41it/s]
 33%|███▎      | 19/57 [00:02<00:05,  7.31it/s]
 35%|███▌      | 20/57 [00:02<00:05,  7.28it/s]
 37%|███▋      | 21/57 [00:03<00:04,  7.27it/s]
 39%|███▊      | 22/57 [00:03<00:04,  7.04it/s]
 40%|████      | 23/57 [00:03<00:04,  7.09it/s]
 42%|████▏     | 24/57 [00:03<00:04,  6.72it/s]
 44%|████▍     | 25/57 [00:03<00:04,  6.85it/s]
 46%|████▌     | 26/57 [00:03<00:04,  6.74it/s]
 47%|████▋     | 27/57 [00:03<00:04,  6.61it/s]
 49%|████▉     | 28/57 [00:04<00:05,  5.76it/s]
 51%|█████     | 29/57 [00:04<00:04,  5.94it/s]
 53%|█████▎    | 30/57 [00:04<00:04,  6.00it/s]
 54%|█████▍    | 31/57 [00:04<00:04,  6.16it/s]
 56%|█████▌    | 32/57 [00:04<00:03,  6.28it/s]
 58%|█████▊    | 33/57 [00:04<00:03,  6.27it/s]
 60%|█████▉    | 34/57 [00:05<00:03,  6.25it/s]
 61%|██████▏   | 35/57 [00:05<00:03,  6.31it/s]
 63%|██████▎   | 36/57 [00:05<00:03,  6.42it/s]
 65%|██████▍   | 37/57 [00:05<00:02,  6.72it/s]
 67%|██████▋   | 38/57 [00:05<00:02,  6.72it/s]
 68%|██████▊   | 39/57 [00:05<00:02,  6.88it/s]
 70%|███████   | 40/57 [00:05<00:02,  7.05it/s]
 72%|███████▏  | 41/57 [00:06<00:02,  6.73it/s]
 74%|███████▎  | 42/57 [00:06<00:02,  6.15it/s]
 75%|███████▌  | 43/57 [00:06<00:02,  6.48it/s]
 77%|███████▋  | 44/57 [00:06<00:01,  6.69it/s]
 79%|███████▉  | 45/57 [00:06<00:01,  6.91it/s]
 81%|████████  | 46/57 [00:06<00:01,  5.94it/s]
 82%|████████▏ | 47/57 [00:07<00:01,  6.21it/s]
 84%|████████▍ | 48/57 [00:07<00:01,  6.33it/s]
 86%|████████▌ | 49/57 [00:07<00:01,  6.54it/s]
 88%|████████▊ | 50/57 [00:07<00:01,  6.73it/s]
 89%|████████▉ | 51/57 [00:07<00:00,  6.90it/s]
 91%|█████████ | 52/57 [00:07<00:00,  7.10it/s]